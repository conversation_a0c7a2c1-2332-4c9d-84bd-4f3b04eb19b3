<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>The Tortoise and the Hare</title>
        <meta
            name="description"
            content="Interactive kids picture book reader"
        />
        <script src="https://cdn.tailwindcss.com"></script>
        <script>
            tailwind.config = {
                theme: {
                    extend: {
                        colors: {
                            primary: "#FF6B6B",
                            secondary: "#4ECDC4",
                            accent: "#FFE66D",
                            background: "#F7FFF7",
                        },
                        fontFamily: {
                            sans: ['"Comic Sans MS"', "cursive", "sans-serif"],
                        },
                    },
                },
            };
        </script>
        <style>
            @import url("https://fonts.googleapis.com/css2?family=Comic+Neue:wght@400;700&display=swap");
            body {
                font-family: "Comic Neue", cursive;
            }
            .page-turn {
                animation: slide 0.5s ease-in-out;
            }
            @keyframes slide {
                0% {
                    transform: translateX(0);
                }
                50% {
                    transform: translateX(-10px);
                }
                100% {
                    transform: translateX(0);
                }
            }
        </style>
    </head>
    <body class="min-h-screen bg-background text-gray-800">
        <div class="container mx-auto px-4 py-8 max-w-6xl">
            <!-- Book Reader Container -->
            <div class="bg-accent rounded-3xl shadow-xl overflow-hidden">
                <!-- Book Pages -->
                <div
                    id="book-container"
                    class="relative h-[80vh] overflow-hidden"
                >
                    <!-- Pages will be inserted here by JavaScript -->
                </div>

                <!-- Navigation Controls -->
                <div class="bg-accent p-4 flex justify-between items-center">
                    <button
                        id="prev-btn"
                        class="bg-primary text-white px-6 py-2 rounded-full hover:bg-opacity-90 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <i class="fas fa-arrow-left"></i> Previous
                    </button>
                    <span id="page-indicator" class="font-bold text-lg"
                        >Page 1 of 20</span
                    >
                    <button
                        id="next-btn"
                        class="bg-primary text-white px-6 py-2 rounded-full hover:bg-opacity-90 transition-all"
                    >
                        Next <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <script>
            // Book data
            const bookPages = [
                {
                    type: "cover",
                    img: "https://lh3.googleusercontent.com/gg/AAHar4dV2Ktl5cH3ub_jjFYyqyyC-qJUmERy7F0Js7oFsmFbRcxsYhzy59sPpaaRKQvt0XvTIQZ5BWTUQQ3rVo_pvaiqfVEfB1O3kPz7-l8yG_xafP_zXZllr_eNCx1ItmvUgPXwkqht3pWwxJuC8bD-NYHGJtbXfOW5zuSdZ2j5cS0Qp30SmYzn=s0",
                    text: "The Tortoise and the Hare",
                },
                {
                    text: 'In a sunny, green meadow, lived a speedy hare named Pip and a slow-and-steady tortoise named Sheldon. Pip loved to brag about how fast he could run. "I\'m the fastest in the whole meadow!" he would boast to anyone who would listen.',
                    img: "https://lh3.googleusercontent.com/gg/AAHar4d3oo4VW9pQOKT3oA38tlT3uPCTiwb-L-6Fmw_Nke1D14KhvnaMZLzuXkLh7F7tYiDKSvSnCcv-ByYZfikEbaHZsMfRfZ54UG7k3m4tF7KSxia7idOJmmEPiGiZGsKLuipAWIeqpOM2x48QPgDLRPUbGW3tePLQtN6H-tJ0p0bTX8_SfuWa=s0",
                },
                {
                    img: "https://lh3.googleusercontent.com/gg/AAHar4dU8oFS3rlZ2tlD1kCDmOZPGb9SJ0lHacNu-1An6BlZG9P4t82FwPsWpnJGt7en0ptWl7udNvSzuqAaa2S0WpkCs0iGweMlFiq_hawm90e4iFXvBHWGaQa6-VYVvEcIjV80H5n9ASNr8Pc-YjlITtk0u5WN8hVzS09PTL5oGQzBi4BxjLBD=s0",
                    text: 'One day, Sheldon the tortoise had heard enough. "Boasting is not winning," he said calmly. "I will race you." Pip the hare laughed so hard he almost fell over. "A race? Between you and me? That\'s the funniest thing I\'ve ever heard!"',
                },
                {
                    img: "https://lh3.googleusercontent.com/gg/AAHar4f2NRDQ0B2TJWhSzzdrNITGhcJqoOxW5pKRUXXbOLKh-3tw9GtSzPIvMefkNqKVAVRBwkgxfm_RxnyPvs57W90BYnGcrnLj3f7_bREIbNz2cjVl63KJoXUAAzJqd02leMT2sY1ezWMm9K6k1hiWcRTU4JKjFOK8Zm4CbNPIoU-M1C349aM=s0",
                    text: "But Sheldon was serious. So, a race was set for the next day. The course would go all the way to the big oak tree on top of the hill. All the other meadow animals gathered to watch.",
                },
                {
                    img: "https://lh3.googleusercontent.com/gg/AAHar4dHvCBvE0VeECKTJ6pTJsBN6-8l9XD09mJI1JF4MRQvRUpNNOddT199ucpxV1XworAJK6xPJ9j0PRdzX0srVW_a_U11NyRv_djzkrMgYcwacm7fotm_edJ-MSVw-i811gFXd13wO9jtFOkApyNiYopvd9oReEQZovaQUEQ2W13DQCuMT_Ov=s0",
                    text: '"Ready, set, GO!" shouted the wise old owl. Pip shot off like an arrow, leaving Sheldon in a cloud of dust. The crowd cheered for the speedy hare.',
                },
                {
                    img: "https://lh3.googleusercontent.com/gg/AAHar4e1bipersXyBvNtB7aGYyZVDZq7YuS1fmgti3p6xKIqi-xPv-9i6lrlaV87omPrYaAHpZf5ljfj-ewxy9w9FmHmCTZQZM6YF1-7FrEwSOF2-nQFYPGsT2joqVKuRUSdnpM5wUnN81ujmEmuM6BG_d5Tn-sKcVf4jk-eFseCNeDPEW4sLawg=s0",
                    text: "Sheldon just kept going, one slow and steady step after another. He didn't look at how far ahead Pip was. He just kept his eyes on the path. Plod, plod, plod.",
                },
                {
                    img: "https://lh3.googleusercontent.com/gg/AAHar4fc2kBQVHDbwJXh4KkzQMOZl-ev-2ejjtvHU3lce-rTMWIEtICmWkkVtJNNnJCKl-9qUjPq7ui97sv10jZ8H7d_Mz75dy6ybGh6sDDtQHoaCxeipbMO5d155uaGSnfskJ0ZTCixZxShgz6s5PAK9af3kmFSNd2lc2zOPMDEEvUqk-Rmic3_=s0",
                    text: 'Pip was so far ahead that he couldn\'t even see Sheldon anymore. "I have plenty of time," he thought. "I think I\'ll stop for a little snack." He found a patch of delicious, crunchy carrots and nibbled on them happily.',
                },
                {
                    img: "https://lh3.googleusercontent.com/gg/AAHar4fKi9E37bnfSH7rA65_Hu3qUaGiy5laNrJg6GyyQaYb3mAa_T3uKZ6CfK43nT7asijdUglsdjod-L6SecllSzzqaRu8U58Sa0jdXfm-XOv4eyixnUKK2aiqHAyjcRrvbRr1wKw_SCFzhQWWtCB3eRwENBTRA0lizvr89W-Zg33pKShYkR_Q=s0",
                    text: 'After his snack, Pip felt a little sleepy. The sun was warm, and the grass looked so soft. "A quick nap won\'t hurt," he yawned, and he curled up under a shady bush and fell fast asleep.',
                },
                {
                    img: "https://lh3.googleusercontent.com/gg/AAHar4dzZ2vcHm0N1wCEVvsgKgeyoNY9sy-z6BvpR7TRTEglQcFzDaC31zlvVyCLFRvWjdTSs51Ybk46CoErxsR8r-PcB6IcDMI529RvjAYlZ-kjnWKwZzJjewMWmQrSeHslaKbnBFfUF9VwdaHbmXs-BLBRWsxAiJSUpTyOx1VbxYJeHJj9kRI=s0",
                    text: "Meanwhile, Sheldon kept going. He plodded past the carrot patch. He plodded past the shady bush where Pip was snoring. He never stopped. He just kept his slow and steady pace, his eyes on the big oak tree at the top of the hill.",
                },
                {
                    img: "https://lh3.googleusercontent.com/gg/AAHar4elaK_gYrrCHpnRh3bAsw2qi38wcnS1-GaGKTC9Q8bShaxgVykaKtB6cRwj5BYKL8WO0-Rvy-GCVRH9NePY-Kc8FN826NZmBlLGBp6be2CQHs2MeLANe0P5YHpPyAcwL6udZxS3Fu7patMqBh7ypTWwjrW8O7nTBEmAUHX4jTsR48neuli-=s0",
                    text: 'Pip woke up with a start. The sun was lower in the sky. "Oh my!" he exclaimed. "I\'ve slept for too long!" He jumped up and ran as fast as he could towards the finish line. But as he got closer, he saw a surprising sight.',
                },
                {
                    img: "https://lh3.googleusercontent.com/gg/AAHar4eazfk8N1KVvI2pJ7EPoA7Xz-FC5xSGR_ssf_1HMl-KzaQpqgy469gtlpXB20G5AfjniDZJv2cp2H0E1a5oH_RGkyKLfBSGBwloawDeJeRWHXpt1g4HjXYv5ySBiTfpZsPu6jR623ONB_Wf1GCSsqdH68tDVQJMQnEsqbcOfyv30OzDtmc=s0",
                    text: "Sheldon the tortoise was just crossing the finish line! The crowd of animals cheered wildly. Pip couldn't believe it. The slow tortoise had won the race. And from that day on, Pip learned that slow and steady can win the race, and he never bragged about his speed again.",
                },
            ];

            // Current page index
            let currentPage = 0;

            // DOM elements
            const bookContainer = document.getElementById("book-container");
            const prevBtn = document.getElementById("prev-btn");
            const nextBtn = document.getElementById("next-btn");
            const pageIndicator = document.getElementById("page-indicator");

            // Initialize the book
            function initBook() {
                renderPage();
                updateNavigation();
            }

            // Render current page
            function renderPage() {
                bookContainer.innerHTML = "";
                const page = bookPages[currentPage];

                if (page.type == "cover") {
                    // Cover page or image-only page
                    const pageDiv = document.createElement("div");
                    pageDiv.className =
                        "absolute inset-0 flex items-center justify-center p-8";

                    const img = document.createElement("img");
                    img.src = page.img;
                    img.alt = "Book illustration";
                    img.className = "h-full w-full object-contain";
                    img.loading = "lazy";

                    if (page.text) {
                        // Cover page with text
                        const textDiv = document.createElement("div");
                        textDiv.className =
                            "absolute bottom-10 left-0 right-0 text-center";
                        textDiv.innerHTML = `<h1 class="text-3xl md:text-5xl font-bold text-white bg-primary bg-opacity-80 py-2 px-4 rounded-lg">${page.text}</h1>`;
                        pageDiv.appendChild(img);
                        pageDiv.appendChild(textDiv);
                    } else {
                        // Image-only page
                        pageDiv.appendChild(img);
                    }

                    bookContainer.appendChild(pageDiv);
                } else {
                    // Text-only page with horizontal split
                    const pageDiv = document.createElement("div");
                    pageDiv.className =
                        "absolute inset-0 flex flex-col md:flex-row";

                    // Left side (text)
                    const textDiv = document.createElement("div");
                    textDiv.className =
                        "flex-1 flex flex-col items-center justify-center p-6 bg-accent overflow-y-auto";
                    textDiv.innerHTML = `<div class="max-w-lg"><p class="text-xl md:text-2xl leading-relaxed">${page.text.replace(/\n/g, "<br>")}</p></div>`;

                    // Right side (image - using previous image for context)
                    const imgDiv = document.createElement("div");
                    imgDiv.className =
                        "flex-1 flex items-center justify-center bg-accent";
                    const img = document.createElement("img");
                    img.src = page.img;
                    ("https://picsum.photos/800/600?random=1");
                    img.alt = "Book illustration";
                    img.className = "h-full w-full object-contain";
                    img.loading = "lazy";
                    imgDiv.appendChild(img);

                    pageDiv.appendChild(textDiv);
                    pageDiv.appendChild(imgDiv);
                    bookContainer.appendChild(pageDiv);
                }

                pageIndicator.textContent = `Page ${currentPage + 1} of ${bookPages.length}`;
            }

            // Update navigation buttons
            function updateNavigation() {
                prevBtn.disabled = currentPage === 0;
                nextBtn.disabled = currentPage === bookPages.length - 1;
            }

            // Event listeners
            prevBtn.addEventListener("click", () => {
                if (currentPage > 0) {
                    currentPage--;
                    bookContainer.classList.add("page-turn");
                    renderPage();
                    setTimeout(
                        () => bookContainer.classList.remove("page-turn"),
                        500,
                    );
                    updateNavigation();
                }
            });

            nextBtn.addEventListener("click", () => {
                if (currentPage < bookPages.length - 1) {
                    currentPage++;
                    bookContainer.classList.add("page-turn");
                    renderPage();
                    setTimeout(
                        () => bookContainer.classList.remove("page-turn"),
                        500,
                    );
                    updateNavigation();
                }
            });

            // Keyboard navigation
            document.addEventListener("keydown", (e) => {
                if (e.key === "ArrowLeft" && currentPage > 0) {
                    currentPage--;
                    renderPage();
                    updateNavigation();
                } else if (
                    e.key === "ArrowRight" &&
                    currentPage < bookPages.length - 1
                ) {
                    currentPage++;
                    renderPage();
                    updateNavigation();
                }
            });

            // Initialize the book on load
            window.addEventListener("load", initBook);
        </script>
    </body>
</html>
