<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kids Storybook Viewer</title>
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Vue.js framework -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Google Fonts for a friendly look -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&display=swap" rel="stylesheet">
    <style>
        /* Custom styles for the app */
        body {
            font-family: 'Nunito', sans-serif;
        }
        /* New page transition animation for a book-like feel */
        .page-turn-enter-active,
        .page-turn-leave-active {
            transition: all 0.6s ease-in-out;
        }
        .page-turn-enter-from {
            opacity: 0;
            transform: perspective(1500px) rotateY(-25deg) scale(0.95);
        }
        .page-turn-leave-to {
            opacity: 0;
            transform: perspective(1500px) rotateY(25deg) scale(0.95);
        }
    </style>
</head>
<body class="bg-yellow-50 text-gray-800">

    <div id="app" class="min-h-screen flex flex-col items-center justify-center p-4 transition-colors duration-500" :class="appBgColor">
        
        <!-- App Template -->
        <div class="w-full max-w-5xl mx-auto">

            <!-- Story Library View -->
            <div v-if="!selectedStory" class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-yellow-600 mb-2">Story Time!</h1>
                <p class="text-lg md:text-xl text-yellow-700 mb-8">Choose a story to read</p>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-6 md:gap-8">
                    <div v-for="story in stories" :key="story.id" @click="selectStory(story)" class="bg-white p-4 rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transition-all duration-300 cursor-pointer flex flex-col items-center">
                        <img :src="story.cover" alt="Story Cover" class="w-full h-40 object-cover rounded-lg mb-4">
                        <h2 class="text-xl font-bold text-gray-700">{{ story.title }}</h2>
                    </div>
                </div>
            </div>

            <!-- Story Viewer View -->
            <div v-if="selectedStory" class="bg-white rounded-2xl shadow-2xl p-6 md:p-8 w-full">
                <button @click="goHome" class="mb-4 bg-yellow-400 hover:bg-yellow-500 text-white font-bold py-2 px-4 rounded-full transition-colors duration-300 flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-left-circle-fill" viewBox="0 0 16 16"><path d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0m3.5 7.5a.5.5 0 0 1 0 1H5.707l2.147 2.146a.5.5 0 0 1-.708.708l-3-3a.5.5 0 0 1 0-.708l3-3a.5.5 0 1 1 .708.708L5.707 7.5z"/></svg>
                    Back to Library
                </button>
                
                <div class="relative min-h-[450px] md:min-h-[500px] flex items-center justify-center">
                    <transition name="page-turn" mode="out-in">
                        <div :key="currentViewKey" class="w-full">
                            <!-- Cover Page View -->
                            <div v-if="currentPage === 0" class="flex flex-col items-center justify-center text-center">
                                <h1 class="text-3xl md:text-4xl font-bold mb-4" :class="selectedStory.color">{{ selectedStory.title }}</h1>
                                <img :src="selectedStory.cover" alt="Story Cover" class="w-full max-w-sm h-auto object-contain rounded-xl shadow-lg">
                            </div>

                            <!-- The End View -->
                            <div v-else-if="isStoryFinished" class="flex items-center justify-center h-full flex-col text-center min-h-[450px] md:min-h-[500px]">
                                <h2 class="text-3xl md:text-4xl font-bold" :class="selectedStory.color">The End</h2>
                                <p class="text-gray-500 mt-4 mb-8 text-lg">Hope you enjoyed the story!</p>
                                <button @click="startOver" class="px-6 py-3 rounded-full font-bold text-white transition-all duration-300 flex items-center gap-2" :class="selectedStory.buttonColor">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-counterclockwise" viewBox="0 0 16 16">
                                      <path fill-rule="evenodd" d="M8 3a5 5 0 1 1-4.546 2.914.5.5 0 0 0-.908-.417A6 6 0 1 0 8 2z"/>
                                      <path d="M8 4.466V.534a.25.25 0 0 0-.41-.192L5.23 2.308a.25.25 0 0 0 0 .384l2.36 1.966A.25.25 0 0 0 8 4.466"/>
                                    </svg>
                                    Start Over
                                </button>
                            </div>

                            <!-- Two-Page Spread View -->
                            <div v-else class="flex flex-col md:flex-row items-stretch gap-4 md:gap-6">
                                <!-- Left Page -->
                                <div class="w-full md:w-1/2 bg-gray-50 p-4 rounded-lg flex flex-col">
                                    <template v-if="leftPage">
                                        <img :src="leftPage.image" alt="Story Illustration" class="w-full h-48 md:h-56 object-cover rounded-md mb-4 shadow-sm">
                                        <p class="text-base md:text-lg leading-relaxed flex-grow">{{ leftPage.text }}</p>
                                    </template>
                                </div>
                                <!-- Right Page -->
                                <div class="w-full md:w-1/2 bg-gray-50 p-4 rounded-lg flex flex-col">
                                    <template v-if="rightPage">
                                        <img :src="rightPage.image" alt="Story Illustration" class="w-full h-48 md:h-56 object-cover rounded-md mb-4 shadow-sm">
                                        <p class="text-base md:text-lg leading-relaxed flex-grow">{{ rightPage.text }}</p>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </transition>
                </div>
                
                <!-- Navigation Controls -->
                <div class="flex justify-between items-center mt-6">
                    <button @click="prevPage" :disabled="currentPage === 0" class="px-6 py-3 rounded-full font-bold text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2" :class="selectedStory.buttonColor">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-chevron-left" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0"/></svg>
                        Previous
                    </button>
                    <span class="text-lg font-semibold text-gray-600 w-48 text-center">{{ pageIndicator }}</span>
                    <button @click="nextPage" :disabled="isStoryFinished" class="px-6 py-3 rounded-full font-bold text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2" :class="selectedStory.buttonColor">
                        Next
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-chevron-right" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708"/></svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed, onMounted, onUnmounted } = Vue;

        createApp({
            setup() {
                // --- STATE ---
                const stories = ref([
                    {
                        id: 1,
                        title: "Leo the Lion's Lost Roar",
                        cover: 'https://placehold.co/400x400/f97316/ffffff?text=Leo',
                        bgColor: 'bg-orange-50',
                        color: 'text-orange-500',
                        buttonColor: 'bg-orange-500 hover:bg-orange-600',
                        pages: [
                            { image: 'https://placehold.co/600x400/fb923c/ffffff?text=Jungle', text: "In a sunny jungle, lived a little lion named Leo. One morning, Leo woke up and tried to roar, but only a tiny squeak came out! 'Oh no!' he whispered. 'I've lost my roar!'" },
                            { image: 'https://placehold.co/600x400/fb923c/ffffff?text=Monkey', text: "He met a wise old monkey swinging from a tree. 'To find your roar,' chattered the monkey, 'you must find the Laughing Lily flower.' Leo thanked him and set off on his quest." },
                            { image: 'https://placehold.co/600x400/fb923c/ffffff?text=River', text: "He had to cross a bubbly river. He was scared, but he took a deep breath and hopped across the stepping stones, one by one. He was braver than he thought!" },
                            { image: 'https://placehold.co/600x400/fb923c/ffffff?text=Flower', text: "Finally, he found a beautiful, bright flower that seemed to giggle in the breeze. He tickled its petals, and the flower let out a joyful laugh. Leo laughed too, a big, happy, booming ROAR! He had found his roar by finding his happiness." }
                        ]
                    },
                    {
                        id: 2,
                        title: "The Magical Midnight Garden",
                        cover: 'https://placehold.co/400x400/8b5cf6/ffffff?text=Garden',
                        bgColor: 'bg-violet-50',
                        color: 'text-violet-500',
                        buttonColor: 'bg-violet-500 hover:bg-violet-600',
                        pages: [
                            { image: 'https://placehold.co/600x400/a78bfa/ffffff?text=Moon', text: "Lily was a girl who couldn't sleep. One night, she saw a glowing butterfly outside her window. She followed it into her garden, which had transformed under the moonlight." },
                            { image: 'https://placehold.co/600x400/a78bfa/ffffff?text=Flowers', text: "The flowers shimmered with silver light, and the dewdrops on the leaves glittered like diamonds. The garden was awake and alive with magic!" },
                            { image: 'https://placehold.co/600x400/a78bfa/ffffff?text=Friends', text: "She danced with fireflies and listened to the songs of the sleepy daisies. A friendly gnome gave her a sip of sweet nectar that tasted like starlight." },
                            { image: 'https://placehold.co/600x400/a78bfa/ffffff?text=Sleep', text: "Feeling calm and happy, Lily went back to her bed. She closed her eyes, dreaming of her magical midnight garden, and fell fast asleep." }
                        ]
                    },
                    {
                        id: 3,
                        title: "Sami the Star Sailor",
                        cover: 'https://placehold.co/400x400/3b82f6/ffffff?text=Sami',
                        bgColor: 'bg-blue-50',
                        color: 'text-blue-500',
                        buttonColor: 'bg-blue-500 hover:bg-blue-600',
                        pages: [
                            { image: 'https://placehold.co/600x400/60a5fa/ffffff?text=Spaceship', text: "Sami wasn't an ordinary kid. He had a spaceship made from a cardboard box and imagination! Every night, he would blast off to explore the stars." },
                            { image: 'https://placehold.co/600x400/60a5fa/ffffff?text=Moon', text: "His first stop was the Moon. It was bouncy like a trampoline! He met the Man in the Moon, who was made of cheese and gave Sami a cheesy smile." },
                            { image: 'https://placehold.co/600x400/60a5fa/ffffff?text=Mars', text: "Next, he zoomed to Mars, the big red planet. He had a race with a little green alien who was very fast, but Sami's spaceship was faster!" }
                        ]
                    }
                ]);

                const selectedStory = ref(null);
                const currentPage = ref(0); // 0 is cover, 1 is pages 1&2, 3 is pages 3&4, etc.
                const isStoryFinished = ref(false);

                // --- COMPUTED PROPERTIES ---
                const totalContentPages = computed(() => selectedStory.value ? selectedStory.value.pages.length - 1 : 0);
                
                const leftPage = computed(() => {
                    if (!selectedStory.value || currentPage.value === 0) return null;
                    return selectedStory.value.pages[currentPage.value];
                });
                
                const rightPage = computed(() => {
                    if (!selectedStory.value || currentPage.value === 0) return null;
                    const rightPageIndex = currentPage.value + 1;
                    return rightPageIndex < selectedStory.value.pages.length ? selectedStory.value.pages[rightPageIndex] : null;
                });
                
                const pageIndicator = computed(() => {
                    if (!selectedStory.value) return '';
                    if (isStoryFinished.value) return 'The End';
                    if (currentPage.value === 0) return `Cover`;
                    
                    const total = selectedStory.value.pages.length - 1;
                    const leftNum = currentPage.value;
                    const rightNum = currentPage.value + 1;

                    if (rightNum <= total) {
                         return `Pages ${leftNum}-${rightNum} / ${total}`;
                    }
                    return `Page ${leftNum} / ${total}`;
                });
                
                const appBgColor = computed(() => selectedStory.value ? selectedStory.value.bgColor : 'bg-yellow-50');

                const currentViewKey = computed(() => {
                    if (isStoryFinished.value) return 'end-screen';
                    return currentPage.value;
                });

                // --- METHODS ---
                const selectStory = (story) => {
                    const storyWithCover = JSON.parse(JSON.stringify(story));
                    storyWithCover.pages.unshift({ isCover: true });
                    selectedStory.value = storyWithCover;
                    currentPage.value = 0;
                    isStoryFinished.value = false;
                };

                const goHome = () => {
                    selectedStory.value = null;
                    isStoryFinished.value = false;
                };

                const startOver = () => {
                    currentPage.value = 0;
                    isStoryFinished.value = false;
                };

                const nextPage = () => {
                    if (isStoryFinished.value) return;

                    if (currentPage.value === 0) {
                        currentPage.value = 1;
                        return;
                    }
                    
                    // If the next spread would be past the end of the pages array
                    if (currentPage.value + 2 >= selectedStory.value.pages.length) {
                        isStoryFinished.value = true;
                    } else {
                        currentPage.value += 2;
                    }
                };

                const prevPage = () => {
                    if (isStoryFinished.value) {
                        const lastContentIndex = selectedStory.value.pages.length - 1;
                        // Determine the starting index of the last spread
                        currentPage.value = (lastContentIndex % 2 !== 0) ? lastContentIndex : lastContentIndex - 1;
                        isStoryFinished.value = false;
                        return;
                    }

                    if (currentPage.value > 0) {
                        currentPage.value = currentPage.value === 1 ? 0 : currentPage.value - 2;
                    }
                };

                const handleKeydown = (e) => {
                    if (selectedStory.value) {
                        if (e.key === 'ArrowRight') nextPage();
                        else if (e.key === 'ArrowLeft') prevPage();
                        else if (e.key === 'Escape') goHome();
                    }
                };
                
                onMounted(() => window.addEventListener('keydown', handleKeydown));
                onUnmounted(() => window.removeEventListener('keydown', handleKeydown));

                return {
                    stories, selectedStory, currentPage, appBgColor, isStoryFinished,
                    leftPage, rightPage, pageIndicator, totalContentPages, currentViewKey,
                    selectStory, goHome, nextPage, prevPage, startOver
                };
            }
        }).mount('#app');
    </script>
</body>
</html>

